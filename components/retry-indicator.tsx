'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoaderIcon, InfoIcon } from './icons';
import { cn } from '@/lib/utils';

interface RetryData {
  attempt: number;
  maxRetries: number;
  error: string;
  nextRetryIn: number;
}

interface RetryIndicatorProps {
  retryData: RetryData | null;
  isOnline?: boolean;
  hasPendingMessage?: boolean;
  className?: string;
}

export function RetryIndicator({ retryData, isOnline = true, hasPendingMessage = false, className }: RetryIndicatorProps) {
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    if (!retryData) return;

    setCountdown(Math.ceil(retryData.nextRetryIn / 1000));

    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Reset to next cycle instead of clearing
          return Math.ceil(retryData.nextRetryIn / 1000);
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [retryData]);

  if (!retryData) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -10, scale: 0.95 }}
        className={cn(
          'fixed top-4 right-4 z-50 bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-lg max-w-sm',
          className
        )}
      >
        <div className="flex items-start gap-3">
          <div className="shrink-0 mt-0.5">
            <div className="animate-spin">
              <LoaderIcon size={16} />
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <div className={cn("text-amber-600", {
                "text-red-600": !isOnline,
                "text-blue-600": hasPendingMessage && isOnline
              })}>
                <InfoIcon size={14} />
              </div>
              <h4 className={cn("text-sm font-medium text-amber-800", {
                "text-red-800": !isOnline,
                "text-blue-800": hasPendingMessage && isOnline
              })}>
                {!isOnline ? 'Network Disconnected' :
                 hasPendingMessage ? 'Retrying Message' :
                 retryData.error.includes('longer than expected') ? 'Processing Request' : 'Retrying Request'}
              </h4>
            </div>
            
            <p className="text-xs text-amber-700 mb-2">
              Attempt {retryData.attempt} of {retryData.maxRetries}
            </p>
            
            {countdown > 0 && (
              <div className="flex items-center gap-2">
                <div className="flex-1 bg-amber-200 rounded-full h-1.5">
                  <motion.div
                    className="bg-amber-500 h-1.5 rounded-full"
                    style={{
                      width: `${(countdown / Math.ceil(retryData.nextRetryIn / 1000)) * 100}%`
                    }}
                    transition={{ duration: 0.5, ease: 'easeOut' }}
                  />
                </div>
                <span className="text-xs text-amber-600 font-mono">
                  {countdown}s
                </span>
              </div>
            )}
            
            <p className={cn("text-xs mt-2 truncate", {
              "text-amber-600": isOnline,
              "text-red-600": !isOnline,
              "text-blue-600": hasPendingMessage && isOnline
            })} title={retryData.error}>
              {!isOnline ? 'Waiting for network connection...' : retryData.error}
            </p>

            {!isOnline && (
              <div className="text-xs text-red-500 mt-1 flex items-center gap-1">
                <span>🔴</span>
                <span>Offline - Message will retry when connected</span>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
