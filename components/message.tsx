'use client';

import type { ChatRequestOptions, Message } from 'ai';
import cx from 'classnames';
import { motion } from 'framer-motion';
import { memo, useState, type Dispatch, type SetStateAction } from 'react';

import type { Vote } from '@/lib/db/schema';

import type { UIBlock } from './block';
import { DocumentToolCall, DocumentToolResult } from './document';
import { GentleGossipIcon, SparklesIcon, LoaderIcon } from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import equal from 'fast-deep-equal';
import { cn } from '@/lib/utils';
import { MessageEditor } from './message-editor';

const PurePreviewMessage = ({
  chatId,
  message,
  block,
  setBlock,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
}: {
  chatId: string;
  message: Message;
  block: UIBlock;
  setBlock: Dispatch<SetStateAction<UIBlock>>;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[]),
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  return (
    <motion.div
      className="w-full mx-auto max-w-3xl px-4 group/message"
      data-role={message.role}
    >
      <div
        className={cn(
          'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
          {
            'w-full': mode === 'edit',
            'group-data-[role=user]/message:w-fit': mode !== 'edit',
          },
        )}
      >
        <div className="flex flex-col gap-2 w-full">
          {message.experimental_attachments && (
            <div className="flex flex-row justify-end gap-2">
              {message.experimental_attachments.map((attachment) => (
                <PreviewAttachment
                  key={attachment.url}
                  attachment={attachment}
                />
              ))}
            </div>
          )}

          {message.toolInvocations && message.toolInvocations.length > 0 && (
            <div className="flex flex-col gap-4">
              {message.toolInvocations.map((toolInvocation) => {
                const { toolName, toolCallId, state, args } = toolInvocation;

                if (toolName === 'getNodeDetails') {
                  return (
                    <div
                      key={toolCallId}
                      className={cn(
                        'flex items-center gap-2.5 text-xs text-muted-foreground bg-slate-50/50 px-3 py-2 rounded-lg',
                        {
                          'animate-pulse': state === 'call',
                        },
                      )}
                    >
                      <div className="text-blue-500">
                        <SparklesIcon size={12} />
                      </div>
                      <span>
                        {state === 'call'
                          ? 'accessing related knowledge...'
                          : 'accessed related knowledge.'}
                      </span>
                    </div>
                  );
                }

                if (state === 'result') {
                  const { result } = toolInvocation;
                  if (toolName !== 'getNodeDetails') {
                    return null;
                  }

                  return (
                    <div key={toolCallId}>
                      {/* Render nothing or a placeholder if needed for other tools, though the request is to remove them */}
                    </div>
                  );
                }
                if (toolName !== 'getNodeDetails') {
                  return null;
                }
                return (
                  <div
                    key={toolCallId}
                    className={cx({
                      // skeleton: ['getWeather'].includes(toolName), // Removed skeleton for other tools
                    })}
                  >
                    {/* Render nothing or a placeholder if needed for other tools */}
                  </div>
                );
              })}
            </div>
          )}

          {message.content && mode === 'view' && (
            <div className="flex flex-row gap-2 items-start">
              {message.role === 'user' ? (
                <div className="flex items-start gap-3 ml-auto">
                  {/* Loading spinner for mobile - appears above/left of message */}
                  {isLoading && message.role === 'user' && (
                    <div className="flex sm:hidden items-center justify-center mt-1">
                      <div className="animate-spin text-blue-500">
                        <LoaderIcon size={16} />
                      </div>
                    </div>
                  )}

                  <div
                    className={cn('flex flex-col gap-4 text-white px-5 py-4 rounded-xl border border-zinc-200')}
                    style={{
                      background: 'var(--gradient-user-bubble)',
                      boxShadow:
                        '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    <Markdown>{message.content as string}</Markdown>
                  </div>

                  {/* Loading spinner for desktop - appears to the right of message */}
                  {isLoading && message.role === 'user' && (
                    <div className="hidden sm:flex items-center justify-center mt-1">
                      <div className="animate-spin text-blue-500">
                        <LoaderIcon size={16} />
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div
                  className={cn('flex flex-col gap-4 bg-white px-5 py-4 rounded-xl border border-zinc-200')}
                  style={{
                    boxShadow:
                      '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <Markdown>{message.content as string}</Markdown>
                </div>
              )}
            </div>
          )}

          {message.content && mode === 'edit' && (
            <div className="flex flex-row gap-2 items-start">
              <div className="size-8" />

              <MessageEditor
                key={message.id}
                message={message}
                setMode={setMode}
                setMessages={setMessages}
                reload={reload}
              />
            </div>
          )}

          {!isReadonly && (
            <MessageActions
              key={`action-${message.id}`}
              chatId={chatId}
              message={message}
              vote={vote}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>
    </motion.div>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.isLoading && nextProps.isLoading) return false;
    if (prevProps.message.content && nextProps.message.content) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    return true;
  },
);


