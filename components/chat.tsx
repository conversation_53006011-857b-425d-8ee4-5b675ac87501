'use client';

import type { Attachment, Message } from 'ai';
import { useChat } from 'ai/react';
import { AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { useRouter } from 'next/navigation';


import { ChatHeader } from '@/components/chat-header';
import type { Vote } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';

import { Block, type UIBlock } from './block';
import { BlockStreamHandler } from './block-stream-handler';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import { RetryIndicator } from './retry-indicator';
import type { VisibilityType } from './visibility-selector';

export function Chat({
  id,
  initialMessages,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  isUserTemporary,
  isAllMessagesView = false,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  isUserTemporary: boolean;
  isAllMessagesView?: boolean;
}) {
  const { mutate } = useSWRConfig();
  const router = useRouter();

  const [offset, setOffset] = useState(initialMessages.length);
  const [loadingMore, setLoadingMore] = useState(false);
  const [retryData, setRetryData] = useState<{
    attempt: number;
    maxRetries: number;
    error: string;
    nextRetryIn: number;
  } | null>(null);
  // Client-side message sending state with retry logic
  const [isSendingMsg, setIsSendingMsg] = useState(false);
  const [currentRetryAttempt, setCurrentRetryAttempt] = useState(0);
  const [isOnline, setIsOnline] = useState(true);
  const [pendingMessage, setPendingMessage] = useState<{
    content: string;
    options?: any;
  } | null>(null);
  const retryIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Retry configuration
  const MAX_RETRIES = 3;
  const RETRY_DELAYS = useMemo(() => [2000, 4000, 8000], []); // 2s, 4s, 8s

  // Custom fetch function with retry logic
  const fetchWithRetry = useCallback(async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    let lastError: Error = new Error('Unknown error');

    for (let attempt = 1; attempt <= MAX_RETRIES + 1; attempt++) {
      try {
        if (attempt > 1) {
          // Show retry indicator for attempts after the first
          const delay = RETRY_DELAYS[attempt - 2] || 8000;
          setRetryData({
            attempt: attempt - 1,
            maxRetries: MAX_RETRIES,
            error: lastError?.message || 'Request failed',
            nextRetryIn: delay
          });

          console.log(`Retrying request (attempt ${attempt - 1}/${MAX_RETRIES}) in ${delay}ms`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const response = await fetch(input, init);

        // Clear retry data on success
        if (response.ok) {
          setRetryData(null);
          return response;
        }

        // Only retry on server errors (5xx) and network errors, not client errors (4xx)
        if (response.status >= 500) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // For client errors (4xx), return the response without retrying
        setRetryData(null);
        return response;

      } catch (error) {
        lastError = error as Error;
        console.error(`Request attempt ${attempt} failed:`, error);

        // If this was the last attempt, throw the error
        if (attempt === MAX_RETRIES + 1) {
          setRetryData(null);
          throw error;
        }
      }
    }

    throw lastError;
  }, [MAX_RETRIES, RETRY_DELAYS]);

  // Network status detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);



  // Custom retry logic for useChat
  const originalUseChat = useChat({
    id,
    body: { id, modelId: selectedModelId },
    initialMessages,
    onFinish: () => {
      mutate('/api/history');
      setRetryData(null);
      setIsSendingMsg(false);
      setPendingMessage(null);
      setCurrentRetryAttempt(0);

      // Clear any pending retry
      if (retryIntervalRef.current) {
        clearTimeout(retryIntervalRef.current);
        retryIntervalRef.current = null;
      }

      // For temporary users, mark that they have sent messages
      if (isUserTemporary && typeof window !== 'undefined') {
        localStorage.setItem('tempUserHasMessages', 'true');
        localStorage.setItem('tempUserChatId', id);
      }
    },
    onError: (error) => {
      console.error('🚨 Chat request failed in onError:', error);

      // If we don't have a pending message yet, this means the error happened
      // after the message was added to the UI but the API call failed
      if (!pendingMessage && messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage.role === 'user') {
          console.log('🔄 Setting up retry for failed message:', lastMessage.content.substring(0, 50) + '...');
          setPendingMessage({ content: lastMessage.content, options: {} });
          setIsSendingMsg(true);
          setCurrentRetryAttempt(1);

          // Show retry indicator
          setRetryData({
            attempt: 1,
            maxRetries: MAX_RETRIES,
            error: 'Server error - retrying...',
            nextRetryIn: RETRY_DELAYS[0] || 2000
          });
        }
      }
    },
  });

  const {
    messages,
    setMessages,
    handleSubmit: originalHandleSubmit,
    input,
    setInput,
    append: originalAppend,
    isLoading,
    stop,
    reload,
    data: streamingData,
  } = originalUseChat;

  // Enhanced wrapper functions with network-aware retry logic
  const handleSubmit = useCallback((event?: { preventDefault?: () => void }, chatRequestOptions?: any) => {
    setRetryData(null);
    setPendingMessage(null); // Clear any existing pending message
    return originalHandleSubmit(event, chatRequestOptions);
  }, [originalHandleSubmit]);

  const append = useCallback(async (message: any, options?: any) => {
    setRetryData(null);
    setIsSendingMsg(true);

    // Store the message as pending in case we need to retry
    const messageContent = typeof message === 'string' ? message : message.content;
    setPendingMessage({ content: messageContent, options });

    try {
      const result = await originalAppend(message, options);
      setPendingMessage(null); // Clear pending message on success
      return result;
    } catch (error) {
      console.error('❌ Message send failed:', error);

      // Initialize retry attempt counter
      setCurrentRetryAttempt(1);

      // Show retry indicator
      setRetryData({
        attempt: 1,
        maxRetries: MAX_RETRIES,
        error: isOnline ? 'Request failed' : 'Network disconnected',
        nextRetryIn: RETRY_DELAYS[0] || 2000
      });

      // Don't clear pending message - let the auto-retry handle it
      throw error;
    } finally {
      setIsSendingMsg(false);
    }
  }, [originalAppend, isOnline]);

  // Network status detection
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      console.log('🌐 Network connection restored');

      // Retry pending message if we have one and haven't exceeded max attempts
      if (pendingMessage && !isSendingMsg && currentRetryAttempt < MAX_RETRIES) {
        console.log('🔄 Retrying pending message after network restoration');
        retryPendingMessage();
      } else if (currentRetryAttempt >= MAX_RETRIES) {
        console.log('❌ Max retries reached, not retrying on network restoration');
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      console.log('📡 Network connection lost');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check initial network status
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [pendingMessage, isSendingMsg]); // retryPendingMessage will be called directly

  // Retry pending message with incremental backoff
  const retryPendingMessage = useCallback(async () => {
    if (!pendingMessage || isSendingMsg || currentRetryAttempt >= MAX_RETRIES) {
      if (currentRetryAttempt >= MAX_RETRIES) {
        console.error('❌ Max retries reached, giving up');
        setPendingMessage(null);
        setRetryData(null);
        setCurrentRetryAttempt(0);
        setIsSendingMsg(false);
      }
      return;
    }

    const nextAttempt = currentRetryAttempt + 1;
    const delay = RETRY_DELAYS[currentRetryAttempt] || RETRY_DELAYS[RETRY_DELAYS.length - 1];

    console.log(`🔄 Retrying pending message (attempt ${nextAttempt}/${MAX_RETRIES}):`, pendingMessage.content.substring(0, 50) + '...');
    console.log(`⏱️ Next retry in ${delay}ms`);

    setCurrentRetryAttempt(nextAttempt);
    setRetryData({
      attempt: nextAttempt,
      maxRetries: MAX_RETRIES,
      error: isOnline ? 'Request failed' : 'Network disconnected',
      nextRetryIn: delay
    });

    // Clear any existing timeout
    if (retryIntervalRef.current) {
      clearTimeout(retryIntervalRef.current);
    }

    // Set up retry with incremental backoff
    retryIntervalRef.current = setTimeout(async () => {
      if (!pendingMessage || currentRetryAttempt >= MAX_RETRIES) return;

      setIsSendingMsg(true);
      try {
        // Use reload() to retry the last request - this is the proper way with useChat
        console.log(`🔄 Executing retry attempt ${nextAttempt} using reload()...`);
        const result = await reload();

        if (result) {
          // Success - clear everything
          setPendingMessage(null);
          setRetryData(null);
          setCurrentRetryAttempt(0);
          console.log('✅ Retry successful!');
        } else {
          throw new Error('Reload returned null/undefined');
        }
      } catch (error) {
        console.error(`❌ Retry attempt ${nextAttempt} failed:`, error);
        // Will be retried by the auto-retry effect if attempts remain
      } finally {
        setIsSendingMsg(false);
      }
    }, delay);
  }, [pendingMessage, isSendingMsg, currentRetryAttempt, MAX_RETRIES, RETRY_DELAYS, isOnline, reload]);

  // Auto-retry logic for pending messages with incremental backoff
  useEffect(() => {
    if (!pendingMessage || !isOnline || isSendingMsg || currentRetryAttempt >= MAX_RETRIES) {
      return;
    }

    // Only start auto-retry if we haven't already scheduled one
    if (!retryIntervalRef.current) {
      console.log('🔄 Starting auto-retry sequence...');
      retryPendingMessage();
    }
  }, [pendingMessage, isOnline, isSendingMsg, currentRetryAttempt, MAX_RETRIES, retryPendingMessage]);

  // Clear retry data when loading completes
  useEffect(() => {
    if (!isLoading) {
      setRetryData(null);
      setIsSendingMsg(false);
    }
  }, [isLoading]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryIntervalRef.current) {
        clearTimeout(retryIntervalRef.current);
      }
    };
  }, []);

  // Track when we're loading older messages to prevent auto-scroll to bottom
  const isLoadingOlderMessagesRef = useRef(false);

  const loadMoreMessages = useCallback(async () => {
    if (loadingMore) return;

    setLoadingMore(true);
    isLoadingOlderMessagesRef.current = true; // Set flag to prevent auto-scroll

    try {
      const container = messagesContainerRef.current;
      if (!container) return;

      // Store the current scroll height before loading new messages
      const prevScrollHeight = container.scrollHeight;

      let response;
      if (isAllMessagesView) {
        // For all messages view, fetch more messages from all conversations
        response = await fetch(
          `/api/chat/all-messages-paginated?limit=50&offset=${messages.length}`,
        );
      } else {
        // For individual chat view, fetch more messages from specific chat
        response = await fetch(
          `/api/chat/${id}?limit=50&offset=${messages.length}&messagesOnly=true&orderBy=desc`,
        );
      }

      if (!response.ok) {
        throw new Error('Failed to fetch more messages');
      }
      const newMessages: Message[] = await response.json();

      if (newMessages.length > 0) {
        setMessages((prevMessages) => {
          // Create a Set of existing message IDs for quick lookup
          const existingIds = new Set(prevMessages.map((msg) => msg.id));
          // Filter out any duplicate messages from newMessages
          const uniqueNewMessages = newMessages.filter(
            (msg) => !existingIds.has(msg.id),
          );
          // Add new messages at the beginning since they're older
          return [...uniqueNewMessages, ...prevMessages];
        });

        // After the new messages are rendered, adjust scroll position
        requestAnimationFrame(() => {
          if (container) {
            const newScrollHeight = container.scrollHeight;
            const addedHeight = newScrollHeight - prevScrollHeight;
            container.scrollTop = addedHeight;
          }
          // Reset the flag after scroll position is adjusted
          setTimeout(() => {
            isLoadingOlderMessagesRef.current = false;
          }, 100);
        });
      } else {
        // Reset flag even if no new messages were loaded
        isLoadingOlderMessagesRef.current = false;
      }
    } catch (error) {
      console.error('Error loading more messages:', error);
      isLoadingOlderMessagesRef.current = false; // Reset flag on error
    } finally {
      setLoadingMore(false);
    }
  }, [
    loadingMore,
    setLoadingMore,
    messagesContainerRef,
    id,
    messages,
    setMessages,
    isAllMessagesView,
  ]); // Removed unnecessary isUserTemporary dependency

  // Throttled scroll handler to improve performance
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTimeRef = useRef<number>(0);

  const throttledHandleScroll = useCallback(() => {
    const now = Date.now();
    const timeSinceLastCall = now - lastScrollTimeRef.current;

    if (timeSinceLastCall >= 100) {
      // Execute immediately if enough time has passed
      lastScrollTimeRef.current = now;
      const container = messagesContainerRef.current;
      if (container && container.scrollTop === 0 && !loadingMore) {
        loadMoreMessages();
      }
    } else {
      // Schedule execution for later
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
      // Ensure timeout value is never negative
      const timeoutDelay = Math.max(1, 100 - timeSinceLastCall);
      throttleTimeoutRef.current = setTimeout(() => {
        lastScrollTimeRef.current = Date.now();
        const container = messagesContainerRef.current;
        if (container && container.scrollTop === 0 && !loadingMore) {
          loadMoreMessages();
        }
      }, timeoutDelay);
    }
  }, [loadingMore, loadMoreMessages]);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', throttledHandleScroll);

    return () => {
      container.removeEventListener('scroll', throttledHandleScroll);
      // Cancel any pending throttled calls
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [id, offset, loadingMore, setMessages, loadMoreMessages, isAllMessagesView, throttledHandleScroll]); // Add dependencies

  // Scroll to bottom when initial messages are loaded (but not when loading older messages)
  useEffect(() => {
    const container = messagesContainerRef.current;
    const hasMessages = messages.length > 0;
    if (container && hasMessages && !isLoadingOlderMessagesRef.current) {
      // Use requestAnimationFrame for non-blocking scroll operation
      requestAnimationFrame(() => {
        container.scrollTop = container.scrollHeight;
      });
    }
  }, [messages.length]); // Include messages.length dependency and extract complex expression

  // Optimized scroll to bottom when new messages are added (during conversation)
  const scrollToBottom = useCallback(() => {
    const container = messagesContainerRef.current;
    if (container) {
      // Use requestAnimationFrame for non-blocking scroll operation
      requestAnimationFrame(() => {
        container.scrollTop = container.scrollHeight;
      });
    }
  }, []);

  const scrollThrottleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollBottomTimeRef = useRef<number>(0);

  const throttledScrollToBottom = useCallback(() => {
    const now = Date.now();
    const timeSinceLastCall = now - lastScrollBottomTimeRef.current;

    if (timeSinceLastCall >= 50) {
      // Execute immediately if enough time has passed
      lastScrollBottomTimeRef.current = now;
      scrollToBottom();
    } else {
      // Schedule execution for later
      if (scrollThrottleTimeoutRef.current) {
        clearTimeout(scrollThrottleTimeoutRef.current);
      }
      // Ensure timeout value is never negative
      const timeoutDelay = Math.max(1, 50 - timeSinceLastCall);
      scrollThrottleTimeoutRef.current = setTimeout(() => {
        lastScrollBottomTimeRef.current = Date.now();
        scrollToBottom();
      }, timeoutDelay);
    }
  }, [scrollToBottom]);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container && messages.length > 0 && !isLoadingOlderMessagesRef.current) {
      // Always scroll to bottom when user sends a new message
      const lastMessage = messages[messages.length - 1];
      const isUserMessage = lastMessage.role === 'user';
      const isAssistantMessage = lastMessage.role === 'assistant';

      if (isUserMessage || isLoading || isAssistantMessage) {
        // Immediately scroll to bottom for user messages, during loading, or AI responses
        scrollToBottom();
      } else {
        // For other cases, check if user is near bottom before auto-scrolling
        const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 100;
        if (isNearBottom) {
          throttledScrollToBottom();
        }
      }
    }
  }, [messages, isLoading, scrollToBottom, throttledScrollToBottom]); // Added dependencies

  // Initialize block with default values to avoid useWindowSize dependency
  const [block, setBlock] = useState<UIBlock>({
    documentId: 'init',
    content: '',
    title: '',
    status: 'idle',
    isVisible: false,
    boundingBox: {
      top: 270, // Default to 1080/4
      left: 480, // Default to 1920/4
      width: 250,
      height: 50,
    },
  });

  // Update block position only when it becomes visible
  useEffect(() => {
    if (block.isVisible) {
      const updateBlockPosition = () => {
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        setBlock(prev => ({
          ...prev,
          boundingBox: {
            ...prev.boundingBox,
            top: windowHeight / 4,
            left: windowWidth / 4,
          },
        }));
      };

      updateBlockPosition();

      // Add throttled resize listener only when block is visible
      let timeoutId: NodeJS.Timeout;
      const handleResize = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(updateBlockPosition, 100);
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
        clearTimeout(timeoutId);
      };
    }
  }, [block.isVisible]);

  // Get message IDs for vote fetching
  const assistantMessageIds = messages
    .filter(msg => msg.role === 'assistant')
    .map(msg => msg.id)
    .filter(id => id); // Filter out any undefined IDs

  const { data: votes } = useSWR<Array<Vote>>(
    assistantMessageIds.length > 0 ? `/api/vote?messageIds=${assistantMessageIds.join(',')}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // Only refetch at most every 60 seconds
    },
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  const handleClearMessages = useCallback(async () => {
    try {
      // Stop any ongoing AI generation first
      stop();

      const response = await fetch('/api/clear-messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to clear messages (${response.status})`);
      }

      const responseData = await response.json();

      // Clear messages from UI immediately
      setMessages([]);

      // Clear any input that might be pending
      setInput('');

      // Reset any block state
      setBlock(prev => ({
        ...prev,
        isVisible: false,
        status: 'idle'
      }));

      // Wait a moment to ensure all operations complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // For temporary users, redirect to new chat if a new chat ID was provided
      if (isUserTemporary && responseData.shouldRedirect && responseData.newChatId) {
        // Store the new chat ID for temporary users and clear the messages flag
        if (typeof window !== 'undefined') {
          localStorage.setItem('tempUserChatId', responseData.newChatId);
          localStorage.removeItem('tempUserHasMessages'); // Clear this since we're starting fresh
        }
        // Redirect to the new chat
        router.push(`/chat/${responseData.newChatId}`);
        return;
      }

      // For all messages view, refetch the filtered messages
      if (isAllMessagesView) {
        try {
          const messagesResponse = await fetch('/api/chat/all-messages-paginated?limit=25&offset=0');
          if (messagesResponse.ok) {
            const filteredMessages = await messagesResponse.json();
            setMessages(filteredMessages);
          }
        } catch (fetchError) {
          console.error('Error fetching filtered messages:', fetchError);
          // If fetching fails, just keep messages cleared
        }
      }

      // Invalidate SWR cache to ensure fresh data on next load
      mutate('/api/history');
    } catch (error) {
      console.error('Error clearing messages:', error);
      throw error;
    }
  }, [setMessages, setInput, setBlock, stop, isAllMessagesView, mutate, isUserTemporary, router]);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          user={null}
          chatId={id}
          selectedModelId={selectedModelId}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
          isUserTemporary={isUserTemporary}
          append={append}
          onClearMessagesAction={handleClearMessages}
        />

        <div className="flex-1 overflow-y-auto" ref={messagesContainerRef}>
          {loadingMore && (
            <div className="flex justify-center py-2 text-gray-500">
              Loading more messages...
            </div>
          )}
          <Messages
            chatId={id}
            block={block}
            setBlock={setBlock}
            isLoading={isLoading}
            votes={votes}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            isUserTemporary={isUserTemporary}
          />
        </div>

        <div className="shrink-0 bg-white">
          <form className="flex mx-auto px-3 md:px-4 pt-4 pb-3 md:py-6 gap-2 w-full md:max-w-3xl">
            {!isReadonly && (
              <MultimodalInput
                chatId={id}
                input={input}
                setInput={setInput}
                handleSubmit={handleSubmit}
                isLoading={isLoading}
                stop={stop}
                attachments={attachments}
                setAttachments={setAttachments}
                messages={messages}
                setMessages={setMessages}
                append={append}
                isUserTemporary={isUserTemporary}
              />
            )}
          </form>
        </div>
      </div>

      <AnimatePresence>
        {block?.isVisible && (
          <Block
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            append={append}
            block={block}
            setBlock={setBlock}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            votes={votes}
            isReadonly={isReadonly}
          />
        )}
      </AnimatePresence>

      <BlockStreamHandler streamingData={streamingData} setBlock={setBlock} />
      <RetryIndicator
        retryData={retryData}
        isOnline={isOnline}
        hasPendingMessage={!!pendingMessage}
      />
    </>
  );
}
